import { Component, EventEmitter, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, FormArray } from '@angular/forms';
import { Title } from '@angular/platform-browser';
import { Router } from '@angular/router';
import { Store } from '@ngrx/store';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { NotificationsService } from 'angular2-notifications';
import { take, takeUntil } from 'rxjs';
import { AppState } from 'src/app/app.reducer';
import { AddCustomForm, DeleteCustomForm, FetchCustomForm, FetchCustomFormFieldExist } from 'src/app/reducers/custom-form/custom-form.action';
import { getCustomForm, getCustomFormFieldExist } from 'src/app/reducers/custom-form/custom-form.reducer';
import { UpdateGlobalSettings } from 'src/app/reducers/global-settings/global-settings.actions';
import { getGlobalSettingsAnonymous } from 'src/app/reducers/global-settings/global-settings.reducer';
import { getPermissions } from 'src/app/reducers/permissions/permissions.reducers';
import { HeaderTitleService } from 'src/app/services/shared/header-title.service';

@Component({
  selector: 'property-settings',
  templateUrl: './property-settings.component.html',
})
export class PropertySettingsComponent implements OnInit, OnDestroy {
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  canView: boolean;
  canUpdate: boolean;
  propertySettingsForm: FormGroup;
  message: string;
  notes: string;
  settingProperty: any;
  settingCustom: any;
  isProjectSettings: boolean;
  isMicrositeOpen: boolean = false;
  projectTypeList: Array<any> = JSON.parse(
    localStorage.getItem('projectType') || '[]'
  );
  projectSubType: any = [];
  customFormSettings: FormGroup;
  unitBlockData: any;
  existingUnitFields: string[] = [];
  existingBlockFields: string[] = [];
  constructor(
    private headerTitle: HeaderTitleService,
    private _store: Store<AppState>,
    private fb: FormBuilder,
    private modalService: BsModalService,
    private modalRef: BsModalRef,
    public metaTitle: Title,
    private router: Router,
    private _notificationService: NotificationsService,
  ) { }

  getAllProjectSubTypes(selectedTypeIds: any[]): any[] {
    let subTypes: any[] = [];
    this.projectTypeList?.forEach((type: any) => {
      if (!selectedTypeIds || selectedTypeIds.length === 0 || selectedTypeIds.includes(type.id)) {
        type.childTypes?.forEach((subType: any) => {
          subTypes.push({
            ...subType,
            projectType: type.displayName,
          });
        });
      }
    });
    return subTypes;
  }

  ngOnInit(): void {
    this.propertySettingsForm = this.fb.group({
      exportProperty: [null],
      exportProject: [null],
      shouldEnableEnquiryForm: [null],
      isProjectMicrositeEnabled: [null],
      showMoreMicrositeProperties: [null],
    });

    this.customFormSettings = this.fb.group({
      projectType: [null],
      projectSubType: [null],
      unitFormField: this.fb.array([]),
      blockFormField: this.fb.array([]),
    })

    this._store.dispatch(new FetchCustomForm());
    this._store
      .select(getCustomForm)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.unitBlockData = data;
      }
      )
    this.metaTitle.setTitle('CRM | Global Config');
    this.headerTitle.setLangTitle('SIDEBAR.global-config');
    this._store
      .select(getPermissions)
      .pipe(takeUntil(this.stopper))
      .subscribe((permissions: any) => {
        if (!permissions?.length) return;
        if (permissions?.includes('Permissions.GlobalSettings.View'))
          this.canView = true;
        if (permissions?.includes('Permissions.GlobalSettings.Update'))
          this.canUpdate = true;
      });
    this.customFormSettings.controls['projectType'].valueChanges.subscribe(
      (val: any[]) => {
        this.projectSubType = this.getAllProjectSubTypes(val);
        this.customFormSettings.patchValue({
          projectSubType: null,
        });
        this.filterAndPopulateFields();
      }
    );
    this.customFormSettings.controls['projectSubType'].valueChanges.subscribe(
      () => {
        this.filterAndPopulateFields();
      }
    );
    this.patchValues();
  }

  filterAndPopulateFields() {
    const items = Array.isArray(this.unitBlockData) ? this.unitBlockData : this.unitBlockData?.items || [];

    let selectedProjectType = this.customFormSettings.get('projectType')?.value;
    const selectedProjectSubType = this.customFormSettings.get('projectSubType')?.value;

    if (Array.isArray(selectedProjectType)) {
      selectedProjectType = selectedProjectType[0];
    }

    if (!selectedProjectType || !selectedProjectSubType) {
      this.populateFormFields([], []);
      return;
    }

    const unitFields = items
      .filter((item: any) =>
        item.module?.toLowerCase() === 'unit' &&
        item.entityId === selectedProjectType &&
        item.entityChildId === selectedProjectSubType
      )
      .map((item: any) => item.fieldDisplayName);

    const blockFields = items
      .filter((item: any) =>
        item.module?.toLowerCase() === 'block' &&
        item.entityId === selectedProjectType &&
        item.entityChildId === selectedProjectSubType
      )
      .map((item: any) => item.fieldDisplayName);

    this.populateFormFields(unitFields, blockFields);
  }

  populateFormFields(unitFields: string[], blockFields: string[]) {
    const unitFormArray = this.customFormSettings.get('unitFormField') as FormArray;
    const blockFormArray = this.customFormSettings.get('blockFormField') as FormArray;
    unitFormArray.clear();
    blockFormArray.clear();
    unitFields.forEach(field => unitFormArray.push(this.fb.control(field)));
    blockFields.forEach(field => blockFormArray.push(this.fb.control(field)));

    // Store existing fields to track what's already in database
    this.existingUnitFields = [...unitFields];
    this.existingBlockFields = [...blockFields];
  }
  patchValues() {
    this._store
      .select(getGlobalSettingsAnonymous)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.settingProperty = data;
        const projectTypeVal = Array.isArray(data?.projectType) ? data.projectType : data?.projectType ? [data.projectType] : [];
        this.propertySettingsForm.patchValue({
          exportProperty: data.isPropertiesExportEnabled,
          exportProject: data.isProjectsExportEnabled,
          shouldEnableEnquiryForm: data.shouldEnableEnquiryForm,
          isProjectMicrositeEnabled: data.isProjectMicrositeEnabled,
          showMoreMicrositeProperties: data?.showMoreMicrositeProperties,
          projectType: projectTypeVal,
          projectSubType: data?.projectSubType,
        });
        this.projectSubType = this.getAllProjectSubTypes(projectTypeVal);
      });
  }

  openConfirmModal(changePopup: any, settingType: string) {
    this.modalRef = this.modalService.show(changePopup, {
      class: 'modal-600 top-modal ip-modal-unset',
      ignoreBackdropClick: true,
      keyboard: false,
    });
    switch (settingType) {
      case 'exportProperty':
        if (this.propertySettingsForm?.value?.exportProperty) {
          this.message =
            'Are you sure you want to disable the “Export” option?';
          this.notes =
            'Users without export permission will not be able to export property anymore.';
        } else {
          this.message = 'Are you sure you want to enable the “Export” option?';
          this.notes =
            'Users with export permission will be able to export property.';
        }
        break;
      case 'exportProject':
        if (this.propertySettingsForm?.value?.exportProject) {
          this.message =
            'Are you sure you want to disable the “Export” option?';
          this.notes =
            'Users without export permission will not be able to export project anymore.';
        } else {
          this.message = 'Are you sure you want to enable the “Export” option?';
          this.notes =
            'Users with export permission will be able to export project.';
        }
        break;
      case 'shouldEnableEnquiryForm':
        if (this.propertySettingsForm?.value?.shouldEnableEnquiryForm) {
          this.message =
            'Are you sure you want to disable the “Enquiry Form” option?';
          this.notes =
            'Users will not be able to see the enquiry form.';
        } else {
          this.message = 'Are you sure you want to enable the “Enquiry Form” option?';
          this.notes =
            'Users will be able to see the enquiry form.';
        }
        break;
      case 'isProjectMicrositeEnabled':
        if (this.propertySettingsForm?.value?.isProjectMicrositeEnabled) {
          this.message =
            'Are you sure you want to disable the “Project Microsite” option?';
          this.notes = 'Users will not be able to see the project microsite.';
        } else {
          this.message =
            'Are you sure you want to enable the “Project Microsite” option?';
          this.notes = 'Users will be able to see the project microsite.';
        }
        break;
      case 'showMoreMicrositeProperties':
        if (this.propertySettingsForm?.value?.showMoreMicrositeProperties) {
          this.message =
            'Are you sure you want to disable the “Show More Properties” option?';
          this.notes =
            'Users will not be able to see the show more properties.';
        } else {
          this.message = 'Are you sure you want to enable the " Show More Properties" Option?';
          this.notes =
            'Users will be able to see the show more properties.';
        }
        break;
    }
  }

  onSave() {
    const settingsProperty: any = this.propertySettingsForm?.value;
    let payload: any = { ...this.settingProperty };
    payload.isProjectsExportEnabled = settingsProperty.exportProject;
    payload.isPropertiesExportEnabled = settingsProperty.exportProperty;
    payload.shouldEnableEnquiryForm = settingsProperty.shouldEnableEnquiryForm
    payload.showMoreMicrositeProperties = settingsProperty.showMoreMicrositeProperties,
      payload.isProjectMicrositeEnabled = settingsProperty.isProjectMicrositeEnabled;
    this._store.dispatch(new UpdateGlobalSettings(payload));
    this.modalRef.hide();
  }

  onSaveCustomForm(unitFormInput?: HTMLInputElement, blockFormInput?: HTMLInputElement) {
    const settingCustom: any = this.customFormSettings?.value;
    const unitFormArray = this.customFormSettings.get('unitFormField') as FormArray;
    const blockFormArray = this.customFormSettings.get('blockFormField') as FormArray;

    // Add input value if present and not already in array
    if (unitFormInput && unitFormInput.value && !unitFormArray.value.includes(unitFormInput.value)) {
      unitFormArray.push(this.fb.control(unitFormInput.value));
      unitFormInput.value = '';
    }
    if (blockFormInput && blockFormInput.value && !blockFormArray.value.includes(blockFormInput.value)) {
      blockFormArray.push(this.fb.control(blockFormInput.value));
      blockFormInput.value = '';
    }

    const fields: any[] = [];
    const entityId = settingCustom.projectType;
    const entityChildId = settingCustom.projectSubType;

    // Only send NEW unit fields that aren't already in database
    const currentUnitFields = this.customFormSettings.value.unitFormField || [];
    const newUnitFields = currentUnitFields.filter((field: string) =>
      !this.existingUnitFields.includes(field)
    );

    newUnitFields.forEach((unitField: string) => {
      fields.push({
        fieldDisplayName: unitField,
        fieldType: 3,
        module: 'Unit',
        entityId: entityId,
        entityChildId: entityChildId
      });
    });

    // Only send NEW block fields that aren't already in database
    const currentBlockFields = this.customFormSettings.value.blockFormField || [];
    const newBlockFields = currentBlockFields.filter((field: string) =>
      !this.existingBlockFields.includes(field)
    );

    newBlockFields.forEach((blockField: string) => {
      fields.push({
        fieldDisplayName: blockField,
        fieldType: 3,
        module: 'Block',
        entityId: entityId,
        entityChildId: entityChildId
      });
    });

    // Only send API call if there are new fields to add
    if (fields.length > 0) {
      const result = { fields };
      this._store.dispatch(new AddCustomForm(result));
      this._store.dispatch(new FetchCustomForm());
    }

    unitFormArray.clear();
    blockFormArray.clear();
    this.customFormSettings.reset();
  }

  closePopup() {
    this.patchValues();
    this.modalRef.hide();
  }

  unitForms() {
    return this.customFormSettings.get('unitFormField') as FormArray;
  }

  blockForms() {
    return this.customFormSettings.get('blockFormField') as FormArray;
  }

  addInputField(value: string, input: HTMLInputElement, formArray: FormArray) {
    if (!value || formArray.value.includes(value)) {
      return;
    }

    // Pick the correct control for error display
    const formFieldControl = formArray === this.unitForms()
      ? this.customFormSettings.get('unitFormField')
      : this.customFormSettings.get('blockFormField');

    if (formFieldControl.errors && formFieldControl.errors['alreadyExist']) {
      return;
    }

    formArray.push(this.fb.control(value));
    input.value = '';
    input.focus();
  }

  removeField(index: number, formArray: FormArray, input: HTMLInputElement) {
    const fieldName = formArray.at(index).value;
    const isUnitField = formArray === this.unitForms();
    const isExistingField = isUnitField
      ? this.existingUnitFields.includes(fieldName)
      : this.existingBlockFields.includes(fieldName);

    if (isExistingField) {
      this.deleteExistingCustomField(fieldName, isUnitField, index, formArray, input);
    } else {
      formArray.removeAt(index);
      input.focus();
      this._notificationService.success('Deleted successfully');
    }
  }

  deleteExistingCustomField(fieldName: string, isUnitField: boolean, index: number, formArray: FormArray, input: HTMLInputElement) {
    const items = Array.isArray(this.unitBlockData) ? this.unitBlockData : this.unitBlockData?.items || [];
    const settingCustom = this.customFormSettings?.value;
    const selectedProjectType = settingCustom.projectType;
    const selectedProjectSubType = settingCustom.projectSubType;

    const fieldToDelete = items.find((item: any) =>
      item.fieldDisplayName === fieldName &&
      item.module?.toLowerCase() === (isUnitField ? 'unit' : 'block') &&
      item.entityId === selectedProjectType &&
      item.entityChildId === selectedProjectSubType
    );

    if (fieldToDelete?.id) {
      this._store.dispatch(new DeleteCustomForm(fieldToDelete.id));
    }
    formArray.removeAt(index);

    if (isUnitField) {
      this.existingUnitFields = this.existingUnitFields.filter(field => field !== fieldName);
    } else {
      this.existingBlockFields = this.existingBlockFields.filter(field => field !== fieldName);
    }
    input.focus();
  }

  validateField(value: string, module: string, index: number) {
    const formArray = this.customFormSettings.get(module === 'Unit' ? 'unitFormField' : 'blockFormField') as FormArray;
    const control = formArray.at(index);
    if (!control) {
      return;
    }
    if (!value) {
      control.setErrors(null);
      return;
    }
    const settingCustom = this.customFormSettings?.value;
    const payload = {
      fieldDisplayName: value,
      module: module,
      entityId: settingCustom.projectType,
      entityChildId: settingCustom.projectSubType
    };
    this._store.dispatch(new FetchCustomFormFieldExist(payload));
    this._store
      .select(getCustomFormFieldExist)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        const isExist = data === true || (data && data.data === true);
        if (isExist) {
          control.setErrors({ alreadyExist: true });
        } else {
          control.setErrors(null);
        }
      });
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}