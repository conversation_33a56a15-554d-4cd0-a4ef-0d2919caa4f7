<h3 class="bg-coal fw-600 align-center flex-between w-100 p-10">
    <div class="align-center"><span class="ic-qr-code icon ic-xl  mr-8"></span>
        <span class="text-white"> {{ 'LEADS.qr-generator' | translate }}</span>
    </div>
    <span class="icon ic-close-secondary ic-large cursor-pointer" (click)="modalRef.hide()"></span>
</h3>
<div class="p-20 align-center-col scrollbar h-100-44">
    <h5 class="fw-semi-bold mt-10">{{ 'LEADS.qr-content' | translate }}</h5>
    <h5 class="fw-600 mt-20">{{subDomain}}</h5>
    <div *ngIf="!data" class="flex-center mt-12 ng-select-sm">
        <div class="field-label mt-0 mr-12">Select Form:</div>
        <ng-select [virtualScroll]="true" [items]="templateList" bindLabel="name" bindValue="id"
            [(ngModel)]="selectedTemplateId" (ngModelChange)="getSelectedId()" placeholder="ex. QR form" class="w-250">
        </ng-select>
    </div>
    <div *ngIf="data && data.templateId" class="flex-center mt-12">
        <h3 class="fw-600 mt-20 text-truncate-1 break-all">{{ data.templateName }}</h3>
    </div>
    <div class="mb-20 text-center">
        <ng-container *ngIf="!qrCode || !qrCode?.length else QRCode">
            <ng-lottie [options]="loader" width="180px" height="180px"></ng-lottie>
        </ng-container>
        <ng-template #QRCode>
            <img [src]="qrCode" alt="" width="240" height="240">
            <div class="flex-center text-light-gray">
                <span [innerHTML]="convertUrlsToLinks(url)"></span>
                <span class="icon ic-copy-clipboard ic-xs ic-light-pale ic-active-black cursor-pointer ml-4"
                    (click)="copyUrl()" [title]="'GLOBAL.copy-link' | translate">
                </span>
            </div>
        </ng-template>
    </div>
    <h5 class="btn br-50px border-accent-green text-accent-green flex-center w-100" (click)="printQRCode()"
        *ngIf="qrCode && qrCode?.length">
        <span class="ic-print icon ic-sm ic-accent-green mr-8"></span>
        {{'LEADS.print' | translate}} {{'LEADS.qr-code' | translate }}
    </h5>
    <h5 class="btn br-50px btn-accent-green text-white flex-center w-100 mt-20" (click)="downloadQRCode()"
        *ngIf="qrCode && qrCode?.length">
        <span class="ic-download icon ic-sm  mr-8"></span>
        {{'INTEGRATION.download' | translate}} {{'LEADS.qr-code' | translate }}
    </h5>
</div>