import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsService } from 'angular2-notifications';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { AnimationOptions } from 'ngx-lottie';
import { map, takeUntil } from 'rxjs';

import { AppState } from 'src/app/app.reducer';
import {
  convertUrlsToLinks,
  getEnvDetails,
  getTenantName,
} from 'src/app/core/utils/common.util';
import { FetchQRCode } from 'src/app/reducers/profile/profile.actions';
import { getQRCode } from 'src/app/reducers/profile/profile.reducers';
import { FetchQrFormsList } from 'src/app/reducers/qr-form/qr-form.action';
import { getQRFormList } from 'src/app/reducers/qr-form/qr-form.reducer';
import { TrackingService } from 'src/app/services/shared/tracking.service';

@Component({
  selector: 'qr-generator',
  templateUrl: './qr-generator.component.html',
})
export class QrGeneratorComponent implements OnInit, OnDestroy {
  @Input() data: any;
  private stopper: EventEmitter<void> = new EventEmitter<void>();
  qrCode: any;
  subDomain = getTenantName();
  loader: AnimationOptions = { path: 'assets/animations/qrCode-loading.json' };
  url: string = '';
  templateList: Array<any> = [];
  selectedTemplateId: any;
  router = '';
  payload: any;
  convertUrlsToLinks = convertUrlsToLinks;
  get isLocalhost() {
    return this.subDomain === 'localhost';
  }

  constructor(
    private store: Store<AppState>,
    public modalRef: BsModalRef,
    private _notificationsService: NotificationsService,
    private _translateService: TranslateService,
    public trackingService: TrackingService
  ) { }

  ngOnInit() {
    if (this.subDomain === 'localhost') {
      const port = window.location.port ? `:${window.location.port}` : '';
      this.router = `${this.subDomain}${port}`;
    } else {
      this.router = this.subDomain + getEnvDetails();
    }
    if (!this.data?.templateId) {
      this.store.dispatch(new FetchQrFormsList());

      this.store
        .select(getQRFormList)
        .pipe(
          takeUntil(this.stopper),
          map((data: any) => data.filter((item: any) => item.status === 0)),
          map((filteredData: any) =>
            filteredData
              .slice()
              .sort((a: any, b: any) => a.name.localeCompare(b.name))
          )
        )
        .subscribe((sortedData: any) => {
          this.templateList = sortedData;
          this.selectedTemplateId = this.templateList?.[0]?.id;

          this.payload = {
            router: this.router,
            templateId: this.selectedTemplateId,
          };
          this.store.dispatch(new FetchQRCode(this.payload));
        });
    } else {
      this.selectedTemplateId = this.data?.templateId;
      this.payload = {
        router: this.router,
        templateId: this.selectedTemplateId,
      };
      this.store.dispatch(new FetchQRCode(this.payload));
    }
    this.store
      .select(getQRCode)
      .pipe(takeUntil(this.stopper))
      .subscribe((data: any) => {
        this.url = `${this.isLocalhost ? window.location.origin : `https://${this.router}`
          }/external/add-lead/qr-code/${this.selectedTemplateId}`;
        this.qrCode = data;
      });
  }

  getSelectedId() {
    this.url = `${this.isLocalhost ? window.location.origin : `https://${this.router}`
      }/external/add-lead/qr-code/${this.selectedTemplateId}`;
    this.payload = {
      ...this.payload,
      templateId: this.selectedTemplateId,
    };
    this.store.dispatch(new FetchQRCode(this.payload));
    this.trackingService.trackFeature('Web.Leads.Options.SelectForm.Click');
  }

  copyUrl(): void {
    navigator.clipboard?.writeText(this.url);
    this._notificationsService.success(
      this._translateService.instant('GLOBAL.link-copied')
    );
  }

  downloadQRCode() {
    var link = document.createElement('a');
    document.body.appendChild(link); // for Firefox
    link.setAttribute('href', this.qrCode);
    link.setAttribute('download', this.subDomain + ' QR Code');
    link.click();
    this.trackingService.trackFeature('Web.Leads.Button.DownloadQRCode.Click');
  }

  printQRCode() {
    const printFrame = document.createElement('iframe');
    printFrame.style.visibility = 'hidden';
    document.body.appendChild(printFrame);
    const printDocument = printFrame.contentWindow.document;
    printDocument.write(`
        <html>
          <head>
            <title></title>
          </head>
          <body style="font-size:46px;text-align:center;">
            <p>${this.subDomain}</p>
            <img src="${this.qrCode}" alt="Image">
          </body>
        </html>
      `);
    printDocument.close();
    printFrame.onload = () => {
      printFrame.contentWindow.print();
      setTimeout(() => {
        document.body.removeChild(printFrame);
      }, 100);
    };
    this.trackingService.trackFeature('Web.Leads.Button.PrintQRCode.Click');
  }

  ngOnDestroy() {
    this.stopper.next();
    this.stopper.complete();
  }
}
