<ng-container *ngIf="canView">
    <div class="pt-12 px-30 position-relative">
        <div class="flex-between">
            <div class="pt-12 align-center">
                <div class="icon ic-chevron-left ic-xxs ic-coal cursor-pointer mr-16" routerLink='/global-config'></div>
                <span class="icon ic-house-solid ic-sm ic-black mr-8"></span>
                <h5 class="fw-600">Projects and Properties {{ 'GLOBAL.settings' | translate }}
                </h5>
            </div>
        </div>
        <form [formGroup]="propertySettingsForm">
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'REPORTS.export' | translate }} Property</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.export-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{propertySettingsForm.get('exportProperty').value == true ? 'on' :
                        'off'}}</div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup,'exportProperty') : ''"
                        formControlName='exportProperty' id="chkexportProperty" name="exportProperty"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkexportProperty" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>

            </div>
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">{{ 'REPORTS.export' | translate }} Project</h5>
                    <h6 class="text-dark-gray pt-4">{{ 'SETTINGS.export-description'| translate }}</h6>
                </div>
                <div class="align-center mr-50 ph-mr-20 ml-20">
                    <div class="text-xs mr-8">{{propertySettingsForm.get('exportProject').value == true ? 'on' : 'off'}}
                    </div>
                    <input type="checkbox" class="toggle-switch toggle-active-sold"
                        (click)="canUpdate ? openConfirmModal(changePopup,'exportProject') : ''"
                        formControlName='exportProject' id="chkexportProject" name="exportProject"
                        [ngClass]="{'pe-none' : !canUpdate}">
                    <label for="chkexportProject" class="switch-label" [ngClass]="{'pe-none' : !canUpdate}"></label>
                </div>

            </div>
            <div class="bg-white pl-20 py-16 mt-20 flex-between br-6">
                <div>
                    <h5 class="fw-600">Amenities and Attributes</h5>
                    <h6 class="text-dark-gray pt-4">you can add or modify amenities and attributes</h6>
                </div>
                <div class="align-center mr-40 ph-mr-20" routerLink='/global-config/amenities-attributes'>
                    <h6 class="cursor-pointer text-black-200 fw-400">Click to manage<span
                            class="ml-20 rotate-90 icon ic-black ic-triangle-up ic-xxxs"></span></h6>
                </div>

            </div>
            <div class="mt-12 bg-white mb-12 br-6">
                <div class="flex-between py-16 pl-20 cursor-pointer" (click)="isCustomField = !isCustomField;"
                    [ngClass]="{'border-bottom' : isCustomField}">
                    <div>
                        <h5 class="fw-600">Manage Microsite
                            <span *ngIf="isCustomField && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span>
                        </h5>
                        <h6 class="text-dark-gray mt-4">update and personalize microsites</h6>
                    </div>
                    <div class="flex-between align-center ml-30">
                        <div class="icon ic-xxs ic-coal mr-50 ph-mr-20"
                            [ngClass]="isCustomField ? 'ic-triangle-up' : 'ic-triangle-down'"></div>
                    </div>
                </div>
                <ng-container *ngIf="isCustomField">
                    <div class="bg-white pl-20 py-16 mt-8 flex-between br-6">
                        <div>
                            <h5 class="fw-600">Enquiry Form</h5>
                            <h6 class="text-dark-gray pt-4">you can enable or disable the enquiry form for your tenant
                            </h6>
                        </div>
                        <div class="align-center mr-50 ph-mr-20 ml-20">
                            <div class="text-xs mr-8">{{propertySettingsForm.get('shouldEnableEnquiryForm').value
                                == true ? 'on' : 'off'}}</div>
                            <input type="checkbox" class="toggle-switch toggle-active-sold"
                                (click)="canUpdate ? openConfirmModal(changePopup, 'shouldEnableEnquiryForm') : ''"
                                formControlName='shouldEnableEnquiryForm' id="chkshouldEnableEnquiryForm"
                                name="shouldEnableEnquiryForm" [ngClass]="{'pe-none' : !canUpdate}">
                            <label for='chkshouldEnableEnquiryForm' class="switch-label"
                                [ngClass]="{'pe-none' : !canUpdate}"></label>
                        </div>
                    </div>
                    <div class="bg-white pl-20 py-16 mt-8 flex-between br-6">
                        <div>
                            <h5 class="fw-600">Show Custom Project Fields</h5>
                            <h6 class="text-dark-gray pt-4">Enable or disable the display of custom project fields on
                                the project microsite
                            </h6>
                        </div>
                        <div class="align-center mr-50 ph-mr-20 ml-20">
                            <div class="text-xs mr-8">{{propertySettingsForm.get('isProjectMicrositeEnabled').value
                                == true ? 'on' : 'off'}}</div>
                            <input type="checkbox" class="toggle-switch toggle-active-sold"
                                (click)="canUpdate ? openConfirmModal(changePopup, 'isProjectMicrositeEnabled') : ''"
                                formControlName='isProjectMicrositeEnabled' id="chkisProjectMicrositeEnabled"
                                name="isProjectMicrositeEnabled" [ngClass]="{'pe-none' : !canUpdate}">
                            <label for='chkisProjectMicrositeEnabled' class="switch-label"
                                [ngClass]="{'pe-none' : !canUpdate}"></label>
                        </div>
                    </div>
                    <div class="bg-white pl-20 py-16 mt-8 flex-between br-6">
                        <div>
                            <h5 class="fw-600">Show more properties</h5>
                            <h6 class="text-dark-gray pt-4">You can enable or disable the show more properties for your
                                tenant
                            </h6>
                        </div>
                        <div class="align-center mr-50 ph-mr-20 ml-20">
                            <div class="text-xs mr-8">{{propertySettingsForm.get('showMoreMicrositeProperties').value
                                == true ? 'on' : 'off'}}</div>
                            <input type="checkbox" class="toggle-switch toggle-active-sold"
                                (click)="canUpdate ? openConfirmModal(changePopup, 'showMoreMicrositeProperties') : ''"
                                formControlName='showMoreMicrositeProperties' id="chkShowMoreMicrositePropertiesEnabled"
                                name="showMoreMicrositeProperties" [ngClass]="{'pe-none' : !canUpdate}">
                            <label for='chkShowMoreMicrositePropertiesEnabled' class="switch-label"
                                [ngClass]="{'pe-none' : !canUpdate}"></label>
                        </div>
                    </div>
                </ng-container>
            </div>
        </form>
        <form [formGroup]="customFormSettings">
            <div class="mt-12 bg-white mb-12 br-6">
                <div class="flex-between py-16 pl-20 cursor-pointer" (click)="isMicrositeOpen = !isMicrositeOpen;"
                    [ngClass]="{'border-bottom' : isMicrositeOpen}">
                    <div>
                        <h5 class="fw-600">Custom project fields
                            <span *ngIf="isMicrositeOpen && false"
                                class="dot dot-sm text-white bg-coal br-50 ml-6 text-sm">?</span>
                        </h5>
                        <h6 class="text-dark-gray mt-4">update and personalize microsites</h6>
                    </div>
                    <div class="flex-between align-center ml-30">
                        <div class="icon ic-xxs ic-coal mr-50 ph-mr-20"
                            [ngClass]="isMicrositeOpen ? 'ic-triangle-up' : 'ic-triangle-down'"></div>
                    </div>
                </div>
                <div *ngIf="isMicrositeOpen">
                    <div class="w-100 py-10 px-24">
                        <div class="w-100 d-flex">
                            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                                <div class="mr-10">
                                    <div for="projectType" class="field-label-req">
                                        Project Type</div>
                                    <form-errors-wrapper label="project type">
                                        <ng-select placeholder="Residential" name="projectType"
                                            [items]="projectTypeList" class="ng-select-xs manage-dropdown" bindValue="id"
                                            bindLabel="displayName" ResizableDropdown [multiple]="false"
                                            [searchable]="true" [closeOnSelect]="true" formControlName="projectType">
                                        </ng-select>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                                <div class="mr-10">
                                    <div class="field-label-req">Project Sub-Type</div>
                                    <form-errors-wrapper [control]="customFormSettings.controls['projectSubType']"
                                        label="Sub type">
                                        <ng-select [virtualScroll]="true" ResizableDropdown [items]="projectSubType"
                                            [multiple]="false" [searchable]="true" [closeOnSelect]="true" class="manage-dropdown"
                                            formControlName="projectSubType" bindLabel="displayName" bindValue="id"
                                            name="displayName"
                                            [readonly]="customFormSettings.controls['projectType'].value ? false : true"
                                            placeholder="ex. Plot">

                                        </ng-select>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                                <div class="mr-10">
                                    <div class="field-label">Unit Form Field Name</div>
                                    <form-errors-wrapper label="unit Form Field Name"
                                        [control]="customFormSettings?.controls['unitFormField']">
                                        <div class="flex-between position-relative">
                                            <input type="text" placeholder="ex. <EMAIL>" #unitFormInput
                                                id="unitFormInput" class="outline-0 padd-r pr-36" autocomplete="off"
                                                appDebounceInput [debounceTime]="500"
                                                (debounceEvent)="validateField(unitFormInput.value, 'Unit', 0)"
                                                (keyup.enter)="addInputField(unitFormInput.value, unitFormInput, unitForms())"
                                                [disabled]="!customFormSettings.value.projectType || !customFormSettings.value.projectSubType" />
                                            <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
                                                (click)="addInputField(unitFormInput.value, unitFormInput, unitForms())"
                                                [class.disabled]="!customFormSettings.value.projectType || !customFormSettings.value.projectSubType"
                                                [ngClass]="{'pe-none': !customFormSettings.value.projectType || !customFormSettings.value.projectSubType}">
                                                <span class="icon ic-plus ic-x-xs"></span>
                                            </div>
                                        </div>
                                        <div *ngFor="let control of unitForms().controls; let i = index">
                                            <div *ngIf="control?.errors?.alreadyExist" class="error-message">
                                                This unit field already exists.
                                            </div>
                                        </div>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                            <div class="w-25 tb-w-33 ip-w-50 ph-w-100">
                                <div class="mr-10">
                                    <div class="field-label">Block Form Field Name</div>
                                    <form-errors-wrapper label="Block Form Field Name"
                                        [control]="customFormSettings?.controls['blockFormField']">
                                        <div class="flex-between position-relative">
                                            <input type="text" placeholder="ex. <EMAIL>" #blockFormInput
                                                id="blockFormInput" class="outline-0 padd-r pr-36" autocomplete="off"
                                                (keyup.enter)="addInputField(blockFormInput.value, blockFormInput, blockForms())"
                                                appDebounceInput [debounceTime]="500"
                                                (debounceEvent)="validateField(blockFormInput.value, 'Block', 1)"
                                                [disabled]="!customFormSettings.value.projectType || !customFormSettings.value.projectSubType" />
                                            <div class="bg-black-600 position-absolute top-7 right-6 dot dot-md cursor-pointer"
                                                (click)="addInputField(blockFormInput.value, blockFormInput, blockForms())"
                                                [class.disabled]="!customFormSettings.value.projectType || !customFormSettings.value.projectSubType"
                                                [ngClass]="{'pe-none': !customFormSettings.value.projectType || !customFormSettings.value.projectSubType}">
                                                <span class="icon ic-plus ic-x-xs"></span>
                                            </div>
                                        </div>
                                        <div *ngFor="let control of blockForms().controls; let i = index">
                                            <div *ngIf="control.errors?.alreadyExist" class="error-message">
                                                This block field already exists.
                                            </div>
                                        </div>
                                    </form-errors-wrapper>
                                </div>
                            </div>
                        </div>
                        <div class="px-10 mt-16 border-top"
                            *ngIf="unitForms()?.controls?.length || blockForms()?.controls?.length">
                            <div class="align-center flex-wrap mt-12" *ngIf="unitForms()?.controls?.length">
                                <div class="text-gray mr-10">Selected Unit form fields :- </div>
                                <div *ngFor="let field of unitForms()?.controls; let i = index"
                                    class="align-center bg-light-pearl br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                        field.value }}</span>
                                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                        (click)="removeField(i, unitForms(), unitFormInput)"></span>
                                </div>
                            </div>
                            <div class="align-center flex-wrap mt-12" *ngIf="blockForms()?.controls?.length">
                                <div class="text-gray mr-10">Selected Block form fields :- </div>
                                <div *ngFor="let field of blockForms()?.controls; let i = index"
                                    class="align-center  bg-light-pearl br-12 py-6 px-8 mr-8 mb-8 shadow-hover-sm">
                                    <span class="fw-600 text-sm mr-4 text-black-100 cursor-pointer ">{{
                                        field.value }}</span>
                                    <span class="icon ic-xxs ic-close ic-gray cursor-pointer ml-6 text-red-hover"
                                        (click)="removeField(i, blockForms(), blockFormInput)"></span>
                                </div>
                            </div>
                        </div>
                        <div class="flex-end">
                            <div class="flex-end py-20">
                                <u class="mr-20 fw-semi-bold text-mud cursor-pointer"
                                    (click)="modalRef.hide()">{{'BUTTONS.cancel' |translate }}</u>
                                <button class="btn-coal" (click)="onSaveCustomForm(unitFormInput, blockFormInput)"
                                    [disabled]="!(unitForms()?.length || blockForms()?.length || unitFormInput.value || blockFormInput.value)">
                                    {{ 'GLOBAL.save-confirmation' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </form>
    </div>
    <ng-template #changePopup>
        <div class="p-20">
            <h3 class="text-black-100 fw-semi-bold mb-20">{{message}}</h3>
            <div class="text-black-200 p-10 bg-light-pearl text-large br-4">Note: {{notes}}</div>
            <div class="flex-end mt-30">
                <button class="btn-gray mr-20" (click)="closePopup()" id="clkSettingsNo"
                    data-automate-id="clkSettingsNo">
                    {{ 'GLOBAL.no' | translate }}</button>
                <button class="btn-green" (click)="onSave()" id="clkSettingsYes" data-automate-id="clkSettingsYes">
                    {{ 'GLOBAL.yes' | translate }}</button>
            </div>
        </div>
    </ng-template>
</ng-container>